import React, { useState, useEffect, useRef } from 'react';
import PixelLoader from './PixelLoader';

// 彻底解决iframe Cookie问题的OpenVSCode集成方案 - 使用nginx代理
const OpenVSCodeApp = ({ onLoad, onError }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [authStage, setAuthStage] = useState('loading'); // 'loading', 'ready'
  const iframeRef = useRef(null);
  const loadingTimerRef = useRef(null);
  const progressTimerRef = useRef(null);

  // 安全的代理方案：通过nginx代理统一域名，隐藏真实OpenVSCode地址
  const ideUrl = `/ide/`;

  // 安全措施：防止通过控制台获取真实地址
  useEffect(() => {
    // 禁用右键菜单和开发者工具快捷键（仅在IDE区域）
    const preventDevTools = (e) => {
      // 禁用F12, Ctrl+Shift+I, Ctrl+U等开发者工具快捷键
      if (e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.key === 'u')) {
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    };

    const preventContextMenu = (e) => {
      e.preventDefault();
      return false;
    };

    // 只在组件挂载时添加监听器
    document.addEventListener('keydown', preventDevTools);
    document.addEventListener('contextmenu', preventContextMenu);

    return () => {
      document.removeEventListener('keydown', preventDevTools);
      document.removeEventListener('contextmenu', preventContextMenu);
    };
  }, []);

  // 修复2: 改进的加载进度指示器
  useEffect(() => {
    if (isLoading) {
      let progress = 0;
      progressTimerRef.current = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        setLoadingProgress(progress);
      }, 200);
    } else {
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }
      setLoadingProgress(100);
    }

    return () => {
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
      }
    };
  }, [isLoading]);

  // 修复3: 更可靠的加载超时机制
  useEffect(() => {
    // 设置加载超时，但给OpenVSCode更多时间初始化
    loadingTimerRef.current = setTimeout(() => {
      if (isLoading) {
        console.log('OpenVSCode加载超时，尝试恢复');
        setIsLoading(false);
        // 不直接设置错误，给iframe一个机会
      }
    }, 20000); // 20秒超时

    return () => {
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
    };
  }, [isLoading]);

  // 修复4: 简化的iframe加载处理 - nginx代理解决了认证问题
  const handleIframeLoad = () => {
    console.log('iframe onLoad 事件触发, 当前阶段:', authStage, '当前URL:', iframeRef.current?.src);
    
    if (authStage === 'loading') {
      // nginx代理已自动处理认证，直接处理IDE加载完成
      console.log('OpenVSCode通过nginx代理加载完成');
      setAuthStage('ready');
      setLoadingProgress(90);
      
      // 清除所有计时器
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
        loadingTimerRef.current = null;
      }
      
      // 给OpenVSCode时间完全渲染
      setTimeout(() => {
        console.log('OpenVSCode渲染完成');
        setIsLoading(false);
        setHasError(false);
        setLoadingProgress(100);
        onLoad?.();
      }, 3000);
    }
  };

  const handleIframeError = () => {
    console.log('iframe onError 事件触发');
    setHasError(true);
    setIsLoading(false);
    onError?.();
  };

  // 修复5: 简化的初始化流程 - 直接加载nginx代理的IDE
  useEffect(() => {
    if (authStage === 'loading' && iframeRef.current) {
      console.log('开始通过nginx代理加载OpenVSCode');
      setLoadingProgress(20);
      
      // 直接加载nginx代理的IDE，自动处理认证
      iframeRef.current.src = ideUrl;
    }
  }, [authStage, ideUrl]);

  // 修复6: 简化的加载进度更新
  useEffect(() => {
    if (authStage === 'loading') {
      setLoadingProgress(20);
    } else if (authStage === 'ready') {
      setLoadingProgress(100);
    }
  }, [authStage]);

  if (hasError) {
    return (
      <div className="flex items-center justify-center h-full bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
        <div className="text-center p-8 max-w-md">
          <div className="mb-6">
            <div className="w-20 h-20 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
              <svg className="w-10 h-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
            OpenVSCode 加载失败
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
            OpenVSCode 服务可能正在启动或遇到网络问题。建议尝试刷新页面或在新窗口中直接访问。
          </p>
          <div className="space-y-3">
            <button 
              onClick={() => {
                setHasError(false);
                setIsLoading(true);
                setLoadingProgress(0);
                setAuthStage('loading');
                // 重新开始加载流程
                if (iframeRef.current) {
                  iframeRef.current.src = ideUrl;
                }
              }}
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              重新加载 IDE
            </button>
            <button 
              onClick={() => window.open(ideUrl, '_blank')}
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              在新窗口中打开
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative bg-white dark:bg-gray-900 overflow-hidden">
      {isLoading && (
        <div className="webide-loading absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 z-50">
          <div className="text-center max-w-md">
            <PixelLoader />
            <div className="mt-6 space-y-3">
              <p className="text-lg text-gray-600 dark:text-gray-400">
                正在启动 OpenVSCode...
              </p>
              
              {/* 修复6: 添加真实的加载进度指示 */}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${loadingProgress}%` }}
                ></div>
              </div>
              
              <p className="text-sm text-gray-500 dark:text-gray-500">
                {authStage === 'loading' && "正在加载编辑器..."}
                {authStage === 'ready' && "即将完成..."}
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* 修复7: 分阶段加载iframe，先认证后加载主内容 */}
      <iframe
        ref={iframeRef}
        className="webide-iframe w-full h-full border-0 bg-white dark:bg-gray-900"
        title="OpenVSCode Web IDE"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        // 安全沙箱设置：限制iframe权限，仅允许必要功能
        sandbox="allow-same-origin allow-scripts allow-forms allow-modals allow-pointer-lock"
        // 权限策略：仅允许必要的功能
        allow="clipboard-read; clipboard-write"
        referrerPolicy="no-referrer"
        // 安全属性：防止地址泄露
        loading="lazy"
        importance="high"
        style={{
          minHeight: '500px',
          background: 'white'
        }}
      />
    </div>
  );
};

const WebIDESection = ({ user, onLogin }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [ideReady, setIdeReady] = useState(false);
  const [ideError, setIdeError] = useState(null);
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  // 窗口大小监听器 - 用于自适应调整
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 修复10: 添加活动检测
  useEffect(() => {
    const handleActivity = () => {
      setLastActivity(Date.now());
    };

    window.addEventListener('mousemove', handleActivity);
    window.addEventListener('keypress', handleActivity);
    window.addEventListener('click', handleActivity);

    return () => {
      window.removeEventListener('mousemove', handleActivity);
      window.removeEventListener('keypress', handleActivity);
      window.removeEventListener('click', handleActivity);
    };
  }, []);

  // 键盘快捷键支持 - 仅支持全屏切换
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'F11' && user) {
        e.preventDefault();
        setIsFullscreen(!isFullscreen);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen, user]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };



  // 计算动态高度 - 根据屏幕大小自适应
  const calculateDynamicHeight = () => {
    const { width, height } = windowSize;

    // 移动端
    if (width < 640) {
      return Math.max(450, height * 0.6);
    }
    // 平板端
    else if (width < 1024) {
      return Math.max(600, height * 0.7);
    }
    // 桌面端
    else if (width < 1536) {
      return Math.max(700, height * 0.75);
    }
    // 大屏幕
    else {
      return Math.max(800, height * 0.8);
    }
  };

  return (
    <div className={`
      transition-all duration-300 ease-in-out
      ${isFullscreen 
        ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900' 
        : 'bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700'
      }
    `}>
      {/* 增强的Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
              Web IDE
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {ideReady ? 'OpenVSCode 运行中 • 安全内嵌环境' : 'OpenVSCode 在线开发环境 • 安全集成'}
              {!isFullscreen && (
                <span className="ml-2 text-xs text-blue-500">
                  • 自适应 {Math.round(calculateDynamicHeight())}px
                </span>
              )}
            </p>
          </div>
          {ideReady && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-600 dark:text-green-400 font-medium">就绪</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleFullscreen}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors tooltip"
            title={isFullscreen ? "退出全屏" : "全屏显示 (F11)"}
          >
            {isFullscreen ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* IDE Container - 改进自适应高度 */}
      <div
        className={`
          webide-container
          ${isFullscreen ? 'h-full' : ''}
          bg-white dark:bg-gray-900 transition-all duration-300
        `}
        style={!isFullscreen ? {
          height: `${calculateDynamicHeight()}px`,
          minHeight: '450px'
        } : {}}
      >
        {user ? (
          <OpenVSCodeApp 
            onLoad={() => {
              setIdeReady(true);
              setIdeError(null);
              console.log('Web IDE 加载完成');
            }}
            onError={(error) => {
              console.error('Web IDE 加载错误:', error);
              setIdeError(error);
              setIdeReady(false);
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <div className="text-center p-8 max-w-md">
              <div className="mb-6">
                <div className="w-20 h-20 mx-auto bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                  <svg className="w-10 h-10 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                需要登录访问
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                请先完成 LDAP 认证登录以访问 OpenVSCode Web IDE 开发环境
              </p>
              <button 
                onClick={onLogin}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors shadow-lg"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                立即登录
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(WebIDESection);